// Dashboard-specific type definitions

export interface DashboardCardProps {
  title: string;
  isLoading?: boolean;
  error?: string | null;
  className?: string;
}

// SWOT Analysis types
export interface SWOTItem {
  id: string;
  type: 'strength' | 'weakness' | 'opportunity' | 'threat';
  title: string;
  description: string;
  category: string; // Academic, Personal, Technical, etc.
  createdAt: string;
  updatedAt: string;
  userId: string;
}

export interface SWOTAnalysis {
  strengths: SWOTItem[];
  weaknesses: SWOTItem[];
  opportunities: SWOTItem[];
  threats: SWOTItem[];
  lastUpdated: string;
  userId: string;
}

// Quick Actions types
export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  action: () => void;
  color: string;
  disabled?: boolean;
}

// Study Analytics types for dashboard
export interface DashboardAnalytics {
  todayStudyTime: number; // in minutes
  dailyTarget: number; // in minutes
  weeklyStreak: number; // number of days
  completedPomodoros: number;
  averageSessionDuration: number; // in minutes
  mostStudiedSubject: string;
  targetProgress: number; // percentage 0-100
}

// Today's tasks summary
export interface TodayTasksSummary {
  totalTasks: number;
  completedTasks: number;
  highPriorityTasks: number;
  overdueTasks: number;
  upcomingTasks: number;
}

// Upcoming exams summary
export interface UpcomingExamsSummary {
  totalExams: number;
  thisWeekExams: number;
  nextExam: {
    name: string;
    date: string;
    daysLeft: number;
    preparationProgress: number;
  } | null;
}

// Chapter progress summary
export interface ChapterProgressSummary {
  totalChapters: number;
  completedChapters: number;
  inProgressChapters: number;
  subjectProgress: {
    subject: string;
    completed: number;
    total: number;
    percentage: number;
    color: string;
  }[];
}

// Dashboard navigation item
export interface DashboardNavItem {
  id: string;
  title: string;
  path: string;
  icon: string;
  description?: string;
  badge?: string | number;
  isActive?: boolean;
}

// Dashboard layout configuration
export interface DashboardLayout {
  sidebar: {
    collapsed: boolean;
    width: number;
  };
  grid: {
    columns: number;
    gap: number;
  };
  cards: {
    [key: string]: {
      position: { row: number; col: number };
      size: { width: number; height: number };
      visible: boolean;
    };
  };
}

// Dashboard preferences
export interface DashboardPreferences {
  layout: DashboardLayout;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    enabled: boolean;
    types: string[];
  };
  refreshInterval: number; // in seconds
  compactMode: boolean;
}

// Error types for dashboard
export interface DashboardError {
  component: string;
  message: string;
  timestamp: string;
  retryable: boolean;
}

// Loading states for dashboard components
export interface DashboardLoadingState {
  todayTasks: boolean;
  analytics: boolean;
  upcomingExams: boolean;
  chapterProgress: boolean;
  swotAnalysis: boolean;
  quickActions: boolean;
}

// Dashboard data refresh status
export interface DashboardRefreshStatus {
  lastRefresh: string;
  isRefreshing: boolean;
  nextRefresh: string;
  errors: DashboardError[];
}
