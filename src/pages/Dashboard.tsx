import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useBackgroundTheme } from "@/contexts/BackgroundThemeContext";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning';
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

// Dashboard components (to be created)
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { TodayTasksCard } from "@/components/dashboard/TodayTasksCard";
import { StudyAnalyticsCard } from "@/components/dashboard/StudyAnalyticsCard";
import { QuickActionsCard } from "@/components/dashboard/QuickActionsCard";
import { UpcomingExamsCard } from "@/components/dashboard/UpcomingExamsCard";
import { ChapterProgressCard } from "@/components/dashboard/ChapterProgressCard";
import { SWOTAnalysisCard } from "@/components/dashboard/SWOTAnalysisCard";
import { MessageBubbleIcon } from "@/components/dashboard/MessageBubbleIcon";

// Icons for floating decorations
import { 
  BarChart3, 
  Calendar, 
  CheckSquare, 
  Clock, 
  Target,
  TrendingUp
} from "lucide-react";

export default function Dashboard() {
  useDocumentTitle("Dashboard - IsotopeAI");
  const { user, loading } = useSupabaseAuth();
  const navigate = useNavigate();
  const { getBackgroundStyle } = useBackgroundTheme();

  useEffect(() => {
    if (!loading && !user) {
      navigate("/login");
    }
  }, [user, loading, navigate]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-violet-500"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className={cn("relative min-h-screen w-full text-foreground overflow-hidden", getBackgroundStyle())}>
      <SmallDeviceWarning />

      {/* Animated gradient orbs (Dark mode only) */}
      <div className="absolute top-20 right-[10%] w-64 h-64 rounded-full bg-purple-600/10 blur-[100px] animate-float-slow dark:block hidden"></div>
      <div className="absolute bottom-20 left-[5%] w-80 h-80 rounded-full bg-blue-600/10 blur-[120px] animate-float-slower dark:block hidden"></div>
      <div className="absolute top-[40%] left-[20%] w-40 h-40 rounded-full bg-pink-600/5 blur-[80px] animate-pulse-slow dark:block hidden"></div>

      {/* Floating decorative icons */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div 
          className="absolute top-1/4 right-1/4 text-violet-500/20 dark:text-violet-500/10"
          animate={{ 
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <BarChart3 size={48} />
        </motion.div>
        
        <motion.div 
          className="absolute top-1/3 left-1/4 text-emerald-500/20 dark:text-emerald-500/10"
          animate={{ 
            y: [0, 10, 0],
            x: [0, 10, 0],
            rotate: [0, 10, 0],
          }}
          transition={{ 
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        >
          <Target size={56} />
        </motion.div>
        
        <motion.div 
          className="absolute bottom-1/4 left-32 text-rose-500/20 dark:text-rose-500/10"
          animate={{ 
            y: [0, -15, 0],
            rotate: [0, -3, 0],
          }}
          transition={{ 
            duration: 9,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3
          }}
        >
          <TrendingUp size={36} />
        </motion.div>

        <motion.div 
          className="absolute top-2/3 right-1/3 text-blue-500/20 dark:text-blue-500/10"
          animate={{ 
            y: [0, 8, 0],
            x: [0, -8, 0],
          }}
          transition={{ 
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        >
          <Calendar size={42} />
        </motion.div>
      </div>

      {/* Message Bubble Icon */}
      <MessageBubbleIcon />

      {/* Main Dashboard Layout */}
      <div className="flex min-h-screen">
        {/* Sidebar */}
        <DashboardSidebar />

        {/* Main Content */}
        <main className="flex-1 p-4 md:p-6 lg:p-8 ml-0 md:ml-64 transition-all duration-300">
          {/* Dashboard Header */}
          <motion.div
            className="mb-6 md:mb-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400 mb-2">
              Welcome back, {user.user_metadata?.full_name?.split(' ')[0] || 'Student'}!
            </h1>
            <p className="text-muted-foreground text-sm md:text-base lg:text-lg">
              Here's your productivity overview for today
            </p>
          </motion.div>

          {/* Dashboard Grid - Improved responsive layout */}
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 auto-rows-min"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {/* Mobile: Stack vertically, Tablet: 2 columns, Desktop: 4 columns */}

            {/* Quick Actions - Always single column */}
            <div className="col-span-1">
              <QuickActionsCard />
            </div>

            {/* Today's Tasks - Responsive sizing */}
            <div className="col-span-1 sm:col-span-1 lg:col-span-1">
              <TodayTasksCard />
            </div>

            {/* Study Analytics - Responsive sizing */}
            <div className="col-span-1 sm:col-span-1 lg:col-span-1">
              <StudyAnalyticsCard />
            </div>

            {/* SWOT Analysis - Single column */}
            <div className="col-span-1">
              <SWOTAnalysisCard />
            </div>

            {/* Upcoming Exams - Spans multiple columns on larger screens */}
            <div className="col-span-1 sm:col-span-2 lg:col-span-3 xl:col-span-3">
              <UpcomingExamsCard />
            </div>

            {/* Chapter Progress - Full width on mobile, spans remaining on desktop */}
            <div className="col-span-1 sm:col-span-2 lg:col-span-3 xl:col-span-4">
              <ChapterProgressCard />
            </div>
          </motion.div>

          {/* Mobile spacing at bottom */}
          <div className="h-20 md:h-8"></div>
        </main>
      </div>
    </div>
  );
}
