import {
  TestCategory,
  UpcomingTest,
  TestSyllabus,
  TestMistake,
  TestTakeaway,
  MockTest,
  ChapterProgress
} from '@/types/mockTest';
import { getStudySessions } from "@/utils/supabase";

// Local storage keys
const STORAGE_KEYS = {
  CATEGORIES: 'mocktest-categories',
  UPCOMING_TESTS: 'mocktest-upcoming',
  SYLLABUS: 'mocktest-syllabus',
  ENHANCED_DATA: 'mocktest-enhanced-data', // For storing enhanced fields of existing tests
  MISTAKES: 'mocktest-mistakes',
  TAKEAWAYS: 'mocktest-takeaways',
} as const;

// Generic localStorage utility functions
function getFromStorage<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage key "${key}":`, error);
    return defaultValue;
  }
}

function saveToStorage<T>(key: string, data: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error saving to localStorage key "${key}":`, error);
  }
}

// Test Categories Management
export const categoryStorage = {
  getAll: (userId: string): TestCategory[] => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    return categories.filter(cat => cat.userId === userId);
  },

  create: (category: Omit<TestCategory, 'id' | 'createdAt'>): TestCategory => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    const newCategory: TestCategory = {
      ...category,
      id: `cat-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date().toISOString(),
    };
    categories.push(newCategory);
    saveToStorage(STORAGE_KEYS.CATEGORIES, categories);
    return newCategory;
  },

  update: (id: string, updates: Partial<TestCategory>): TestCategory | null => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    const index = categories.findIndex(cat => cat.id === id);
    if (index === -1) return null;
    
    categories[index] = { ...categories[index], ...updates };
    saveToStorage(STORAGE_KEYS.CATEGORIES, categories);
    return categories[index];
  },

  delete: (id: string): boolean => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    const filteredCategories = categories.filter(cat => cat.id !== id);
    if (filteredCategories.length === categories.length) return false;
    
    saveToStorage(STORAGE_KEYS.CATEGORIES, filteredCategories);
    return true;
  },

  getById: (id: string): TestCategory | null => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    return categories.find(cat => cat.id === id) || null;
  }
};

// Upcoming Tests Management
export const upcomingTestStorage = {
  getAll: (userId: string): UpcomingTest[] => {
    const tests = getFromStorage<UpcomingTest[]>(STORAGE_KEYS.UPCOMING_TESTS, []);
    return tests.filter(test => test.userId === userId);
  },

  getById: (userId: string, id: string): UpcomingTest | null => {
    const tests = upcomingTestStorage.getAll(userId);
    return tests.find(test => test.id === id) || null;
  },

  create: (test: Omit<UpcomingTest, 'id' | 'createdAt' | 'daysLeft'>): UpcomingTest => {
    const tests = getFromStorage<UpcomingTest[]>(STORAGE_KEYS.UPCOMING_TESTS, []);
    const newTest: UpcomingTest = {
      ...test,
      id: `upcoming-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date().toISOString(),
      daysLeft: calculateDaysLeft(test.date),
    };
    tests.push(newTest);
    saveToStorage(STORAGE_KEYS.UPCOMING_TESTS, tests);
    return newTest;
  },

  update: (id: string, updates: Partial<UpcomingTest>): UpcomingTest | null => {
    const tests = getFromStorage<UpcomingTest[]>(STORAGE_KEYS.UPCOMING_TESTS, []);
    const index = tests.findIndex(test => test.id === id);
    if (index === -1) return null;
    
    const updatedTest = { ...tests[index], ...updates };
    if (updates.date) {
      updatedTest.daysLeft = calculateDaysLeft(updates.date);
    }
    
    tests[index] = updatedTest;
    saveToStorage(STORAGE_KEYS.UPCOMING_TESTS, tests);
    return updatedTest;
  },

  delete: (userId: string, id: string): boolean => {
    const tests = getFromStorage<UpcomingTest[]>(STORAGE_KEYS.UPCOMING_TESTS, []);
    const filteredTests = tests.filter(test => test.id !== id);
    if (filteredTests.length === tests.length) return false;

    saveToStorage(STORAGE_KEYS.UPCOMING_TESTS, filteredTests);
    return true;
  },

  getUpcoming: (userId: string, daysAhead: number = 30): UpcomingTest[] => {
    const tests = upcomingTestStorage.getAll(userId);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day to allow same-day tests
    const futureDate = new Date(today.getTime() + (daysAhead * 24 * 60 * 60 * 1000));

    return tests
      .filter(test => {
        const testDate = new Date(test.date);
        testDate.setHours(0, 0, 0, 0); // Set to start of day for comparison
        return testDate >= today && testDate <= futureDate;
      })
      .map(test => ({
        ...test,
        daysLeft: calculateDaysLeft(test.date)
      }))
      .sort((a, b) => (a.daysLeft || 0) - (b.daysLeft || 0));
  }
};

// Syllabus Management
export const syllabusStorage = {
  get: (testId: string): TestSyllabus | null => {
    const syllabi = getFromStorage<TestSyllabus[]>(STORAGE_KEYS.SYLLABUS, []);
    return syllabi.find(syl => syl.testId === testId) || null;
  },

  save: (syllabus: TestSyllabus): void => {
    const syllabi = getFromStorage<TestSyllabus[]>(STORAGE_KEYS.SYLLABUS, []);
    const index = syllabi.findIndex(syl => syl.testId === syllabus.testId);
    
    const updatedSyllabus = {
      ...syllabus,
      overallProgress: calculateSyllabusProgress(syllabus.chapters),
      lastUpdated: new Date().toISOString()
    };
    
    if (index === -1) {
      syllabi.push(updatedSyllabus);
    } else {
      syllabi[index] = updatedSyllabus;
    }
    
    saveToStorage(STORAGE_KEYS.SYLLABUS, syllabi);
  },

  delete: (testId: string): boolean => {
    const syllabi = getFromStorage<TestSyllabus[]>(STORAGE_KEYS.SYLLABUS, []);
    const filteredSyllabi = syllabi.filter(syl => syl.testId !== testId);
    if (filteredSyllabi.length === syllabi.length) return false;
    
    saveToStorage(STORAGE_KEYS.SYLLABUS, filteredSyllabi);
    return true;
  }
};

// Enhanced Mock Test Data Management (for storing enhanced fields)
export const enhancedTestStorage = {
  get: (testId: string): Partial<MockTest> | null => {
    const enhancedData = getFromStorage<Record<string, Partial<MockTest>>>(STORAGE_KEYS.ENHANCED_DATA, {});
    return enhancedData[testId] || null;
  },

  save: (testId: string, data: Partial<MockTest>): void => {
    const enhancedData = getFromStorage<Record<string, Partial<MockTest>>>(STORAGE_KEYS.ENHANCED_DATA, {});
    enhancedData[testId] = { ...enhancedData[testId], ...data };
    saveToStorage(STORAGE_KEYS.ENHANCED_DATA, enhancedData);
  },

  delete: (testId: string): boolean => {
    const enhancedData = getFromStorage<Record<string, Partial<MockTest>>>(STORAGE_KEYS.ENHANCED_DATA, {});
    if (!(testId in enhancedData)) return false;
    
    delete enhancedData[testId];
    saveToStorage(STORAGE_KEYS.ENHANCED_DATA, enhancedData);
    return true;
  },

  getAll: (): Record<string, Partial<MockTest>> => {
    return getFromStorage<Record<string, Partial<MockTest>>>(STORAGE_KEYS.ENHANCED_DATA, {});
  }
};

// Utility functions
function calculateDaysLeft(dateString: string): number {
  const testDate = new Date(dateString);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  testDate.setHours(0, 0, 0, 0);

  const diffTime = testDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// URL validation and utilities
export const urlUtils = {
  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  formatUrl: (url: string): string => {
    if (!url) return '';
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`;
    }
    return url;
  },

  getFileType: (url: string): string => {
    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'PDF';
      case 'doc':
      case 'docx': return 'Word';
      case 'jpg':
      case 'jpeg':
      case 'png': return 'Image';
      default: return 'File';
    }
  },

  getDomainName: (url: string): string => {
    try {
      const domain = new URL(url).hostname;
      return domain.replace('www.', '');
    } catch {
      return 'Unknown';
    }
  }
};

function calculateSyllabusProgress(chapters: any[]): number {
  if (chapters.length === 0) return 0;
  const completedChapters = chapters.filter(ch => ch.isCompleted).length;
  return Math.round((completedChapters / chapters.length) * 100);
}

// Mistakes and Takeaways Management
export const mistakesStorage = {
  getByTest: (testId: string): TestMistake[] => {
    const mistakes = getFromStorage<TestMistake[]>(STORAGE_KEYS.MISTAKES, []);
    return mistakes.filter(mistake => mistake.id.startsWith(testId));
  },

  add: (testId: string, mistake: Omit<TestMistake, 'id' | 'createdAt'>): TestMistake => {
    const mistakes = getFromStorage<TestMistake[]>(STORAGE_KEYS.MISTAKES, []);
    const newMistake: TestMistake = {
      ...mistake,
      id: `${testId}-mistake-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date().toISOString(),
    };
    mistakes.push(newMistake);
    saveToStorage(STORAGE_KEYS.MISTAKES, mistakes);
    return newMistake;
  },

  update: (id: string, updates: Partial<TestMistake>): TestMistake | null => {
    const mistakes = getFromStorage<TestMistake[]>(STORAGE_KEYS.MISTAKES, []);
    const index = mistakes.findIndex(mistake => mistake.id === id);
    if (index === -1) return null;
    
    mistakes[index] = { ...mistakes[index], ...updates };
    saveToStorage(STORAGE_KEYS.MISTAKES, mistakes);
    return mistakes[index];
  },

  delete: (id: string): boolean => {
    const mistakes = getFromStorage<TestMistake[]>(STORAGE_KEYS.MISTAKES, []);
    const filteredMistakes = mistakes.filter(mistake => mistake.id !== id);
    if (filteredMistakes.length === mistakes.length) return false;
    
    saveToStorage(STORAGE_KEYS.MISTAKES, filteredMistakes);
    return true;
  }
};

export const takeawaysStorage = {
  getByTest: (testId: string): TestTakeaway[] => {
    const takeaways = getFromStorage<TestTakeaway[]>(STORAGE_KEYS.TAKEAWAYS, []);
    return takeaways.filter(takeaway => takeaway.id.startsWith(testId));
  },

  add: (testId: string, takeaway: Omit<TestTakeaway, 'id' | 'createdAt'>): TestTakeaway => {
    const takeaways = getFromStorage<TestTakeaway[]>(STORAGE_KEYS.TAKEAWAYS, []);
    const newTakeaway: TestTakeaway = {
      ...takeaway,
      id: `${testId}-takeaway-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date().toISOString(),
    };
    takeaways.push(newTakeaway);
    saveToStorage(STORAGE_KEYS.TAKEAWAYS, takeaways);
    return newTakeaway;
  },

  update: (id: string, updates: Partial<TestTakeaway>): TestTakeaway | null => {
    const takeaways = getFromStorage<TestTakeaway[]>(STORAGE_KEYS.TAKEAWAYS, []);
    const index = takeaways.findIndex(takeaway => takeaway.id === id);
    if (index === -1) return null;

    takeaways[index] = { ...takeaways[index], ...updates };
    saveToStorage(STORAGE_KEYS.TAKEAWAYS, takeaways);
    return takeaways[index];
  },

  delete: (id: string): boolean => {
    const takeaways = getFromStorage<TestTakeaway[]>(STORAGE_KEYS.TAKEAWAYS, []);
    const filteredTakeaways = takeaways.filter(takeaway => takeaway.id !== id);
    if (filteredTakeaways.length === takeaways.length) return false;

    saveToStorage(STORAGE_KEYS.TAKEAWAYS, filteredTakeaways);
    return true;
  }
};

// Enhanced Mock Test Utilities - Now fully local storage based
export const enhancedMockTestUtils = {
  // Storage key for mock tests
  getStorageKey: (userId: string) => `mock_tests_${userId}`,

  // Get all mock tests for a user with enhanced data
  getAll: (userId: string): MockTest[] => {
    try {
      const data = localStorage.getItem(enhancedMockTestUtils.getStorageKey(userId));
      const basicTests: MockTest[] = data ? JSON.parse(data) : [];

      // Merge with enhanced local data
      return basicTests.map(test => enhancedMockTestUtils.mergeWithLocalData(test));
    } catch (error) {
      console.error('Error loading mock tests:', error);
      return [];
    }
  },

  // Merge basic MockTest with enhanced local data
  mergeWithLocalData: (basicTest: MockTest): MockTest => {
    const enhancedData = enhancedTestStorage.get(basicTest.id);
    const storageMistakes = mistakesStorage.getByTest(basicTest.id);
    const storageTakeaways = takeawaysStorage.getByTest(basicTest.id);

    // Prioritize data from the test object itself (for new enhanced tests)
    // Fall back to separate storage for legacy tests and convert to unified format
    const mistakes = basicTest.mistakes && basicTest.mistakes.length > 0
      ? basicTest.mistakes
      : storageMistakes.map(mistake => ({
          id: mistake.id,
          category: 'other' as const,
          subject: mistake.subject,
          topic: mistake.topic,
          description: mistake.description,
          severity: 'medium' as const,
          createdAt: mistake.createdAt,
          resolved: false
        }));

    const takeaways = basicTest.takeaways && basicTest.takeaways.length > 0
      ? basicTest.takeaways
      : storageTakeaways.map(takeaway => ({
          id: takeaway.id,
          category: takeaway.category === 'other' ? 'other' as const : takeaway.category,
          subject: takeaway.subject,
          description: takeaway.description,
          priority: 'medium' as const,
          implemented: false,
          createdAt: takeaway.createdAt
        }));

    return {
      ...basicTest,
      // Set defaults for enhanced fields
      isReviewed: false,
      difficulty: 'medium' as const,
      testType: 'mock' as const,
      targetScore: 0,
      analysisCompleted: false,
      mistakesAnalyzed: false,
      takeawaysRecorded: false,
      status: 'completed' as const,
      // Ensure marksObtained is set for backward compatibility
      marksObtained: basicTest.totalMarksObtained,
      // Override with local data if available
      ...enhancedData,
      // Use prioritized mistakes and takeaways
      mistakes,
      takeaways,
    };
  },

  // Save a mock test to local storage
  save: (userId: string, mockTest: MockTest): MockTest => {
    const tests = enhancedMockTestUtils.getAll(userId);
    const existingIndex = tests.findIndex(t => t.id === mockTest.id);

    const testToSave = {
      ...mockTest,
      createdAt: mockTest.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    let updatedTests: MockTest[];
    if (existingIndex >= 0) {
      updatedTests = [...tests];
      updatedTests[existingIndex] = testToSave;
    } else {
      updatedTests = [...tests, testToSave];
    }

    // Save mistakes and takeaways to their separate storage if they exist
    if (mockTest.mistakes && mockTest.mistakes.length > 0) {
      mockTest.mistakes.forEach(mistake => {
        // Convert UnifiedMistake to legacy format for storage
        mistakesStorage.add(mockTest.id, {
          subject: mistake.subject || '',
          topic: mistake.topic || '',
          description: mistake.description,
          solution: mistake.solution || '',
        });
      });
    }

    if (mockTest.takeaways && mockTest.takeaways.length > 0) {
      mockTest.takeaways.forEach(takeaway => {
        // Convert UnifiedTakeaway to legacy format for storage
        takeawaysStorage.add(mockTest.id, {
          category: takeaway.category === 'time_management' ? 'other' : takeaway.category,
          description: takeaway.description,
          priority: takeaway.priority || 'medium',
        });
      });
    }

    // Save the complete test data (including enhanced fields but excluding mistakes/takeaways to avoid duplication)
    const testsToSave = updatedTests.map(test => {
      const { mistakes, takeaways, ...testWithoutAnalysis } = test;
      return testWithoutAnalysis;
    });

    localStorage.setItem(enhancedMockTestUtils.getStorageKey(userId), JSON.stringify(testsToSave));

    // Save enhanced fields separately
    enhancedMockTestUtils.saveEnhancedFields(mockTest.id, mockTest);

    return testToSave;
  },

  // Update a mock test
  update: (userId: string, testId: string, updates: Partial<MockTest>): MockTest | null => {
    const tests = enhancedMockTestUtils.getAll(userId);
    const testIndex = tests.findIndex(t => t.id === testId);

    if (testIndex === -1) return null;

    const updatedTest = {
      ...tests[testIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    const updatedTests = [...tests];
    updatedTests[testIndex] = updatedTest;

    // Update mistakes and takeaways in their separate storage if they exist in updates
    if (updates.mistakes !== undefined) {
      // Clear existing mistakes for this test
      const existingMistakes = mistakesStorage.getByTest(testId);
      existingMistakes.forEach(mistake => mistakesStorage.delete(mistake.id));

      // Add new mistakes
      if (updates.mistakes.length > 0) {
        updates.mistakes.forEach(mistake => {
          mistakesStorage.add(testId, {
            subject: mistake.subject || '',
            topic: mistake.topic || '',
            description: mistake.description,
            solution: mistake.solution || '',
          });
        });
      }
    }

    if (updates.takeaways !== undefined) {
      // Clear existing takeaways for this test
      const existingTakeaways = takeawaysStorage.getByTest(testId);
      existingTakeaways.forEach(takeaway => takeawaysStorage.delete(takeaway.id));

      // Add new takeaways
      if (updates.takeaways.length > 0) {
        updates.takeaways.forEach(takeaway => {
          takeawaysStorage.add(testId, {
            category: takeaway.category === 'time_management' ? 'other' : takeaway.category,
            description: takeaway.description,
            priority: takeaway.priority || 'medium',
          });
        });
      }
    }

    // Save the complete test data (excluding mistakes/takeaways to avoid duplication)
    const testsToSave = updatedTests.map(test => {
      const { mistakes, takeaways, ...testWithoutAnalysis } = test;
      return testWithoutAnalysis;
    });

    localStorage.setItem(enhancedMockTestUtils.getStorageKey(userId), JSON.stringify(testsToSave));

    // Save enhanced fields separately
    enhancedMockTestUtils.saveEnhancedFields(testId, updatedTest);

    return updatedTest;
  },

  // Delete a mock test
  delete: (userId: string, testId: string): boolean => {
    const tests = enhancedMockTestUtils.getAll(userId);
    const filteredTests = tests.filter(t => t.id !== testId);

    if (filteredTests.length === tests.length) return false;

    // Save basic test data
    const basicTests = filteredTests.map(test => {
      const { mistakes, takeaways, ...basicTest } = test;
      return basicTest;
    });

    localStorage.setItem(enhancedMockTestUtils.getStorageKey(userId), JSON.stringify(basicTests));

    // Clean up enhanced data
    enhancedMockTestUtils.cleanupLocalData(testId);

    return true;
  },

  // Merge multiple tests with their enhanced data
  mergeMultipleWithLocalData: (basicTests: MockTest[]): MockTest[] => {
    return basicTests.map(test => enhancedMockTestUtils.mergeWithLocalData(test));
  },

  // Save enhanced fields to local storage
  saveEnhancedFields: (testId: string, enhancedFields: Partial<MockTest>): void => {
    // Extract only the enhanced fields that should be stored locally
    const {
      categoryId,
      testPaperUrl,
      isReviewed,
      reviewedAt,
      difficulty,
      timeSpent,
      targetScore,
      isFromUpcoming
      // Don't store mistakes and takeaways here - they have their own storage
    } = enhancedFields;

    const dataToStore = {
      categoryId,
      testPaperUrl,
      isReviewed,
      reviewedAt,
      difficulty,
      timeSpent,
      targetScore,
      isFromUpcoming,
    };

    // Remove undefined values
    const cleanedData = Object.fromEntries(
      Object.entries(dataToStore).filter(([_, value]) => value !== undefined)
    );

    if (Object.keys(cleanedData).length > 0) {
      enhancedTestStorage.save(testId, cleanedData);
    }
  },

  // Mark test as reviewed
  markAsReviewed: (testId: string): void => {
    enhancedTestStorage.save(testId, {
      isReviewed: true,
      reviewedAt: new Date().toISOString(),
    });
  },

  // Mark test as unreviewed
  markAsUnreviewed: (testId: string): void => {
    enhancedTestStorage.save(testId, {
      isReviewed: false,
      reviewedAt: undefined,
    });
  },

  // Get all enhanced tests for a user
  getAllEnhancedTests: async (_userId: string, getBasicTests: () => Promise<MockTest[]>): Promise<MockTest[]> => {
    const basicTests = await getBasicTests();
    return enhancedMockTestUtils.mergeMultipleWithLocalData(basicTests);
  },

  // Create a test from upcoming test
  createFromUpcoming: (upcomingTest: UpcomingTest): Partial<MockTest> => {
    return {
      name: upcomingTest.name,
      date: upcomingTest.date,
      categoryId: upcomingTest.categoryId,
      testPaperUrl: upcomingTest.testPaperUrl,
      isFromUpcoming: true,
      isReviewed: false,
      mistakes: [],
      takeaways: [],
      difficulty: 'medium' as const,
    };
  },

  // Clean up local data for deleted test
  cleanupLocalData: (testId: string): void => {
    enhancedTestStorage.delete(testId);
    syllabusStorage.delete(testId);

    // Clean up mistakes and takeaways
    const mistakes = mistakesStorage.getByTest(testId);
    const takeaways = takeawaysStorage.getByTest(testId);

    mistakes.forEach(mistake => mistakesStorage.delete(mistake.id));
    takeaways.forEach(takeaway => takeawaysStorage.delete(takeaway.id));
  },

  // Delete enhanced data for a test (alias for cleanupLocalData)
  deleteEnhancedData: (testId: string): void => {
    enhancedMockTestUtils.cleanupLocalData(testId);
  }
};

// Enhanced D-Day Exam Interface
export interface DDayExam {
  id: string;
  name: string;
  date: string; // YYYY-MM-DD format
  time: string; // HH:MM format
  userId: string;
  status: 'upcoming' | 'completed' | 'missed' | 'in_progress';
  priority: 'low' | 'medium' | 'high' | 'critical';
  category?: string; // Keep for backward compatibility
  categories?: string[]; // New field for multiple categories
  description?: string;
  reminderSettings: {
    enabled: boolean;
    intervals: number[]; // Days before exam to remind
    lastReminded?: string;
  };
  preparationData: {
    chapters: ChapterProgress[]; // Chapter-based progress tracking
    totalTopics: string[]; // Keep for backward compatibility
    lastStudySession?: string;
  };
  syncedFromUpcoming: boolean; // Whether this came from upcoming tests
  createdAt: string;
  updatedAt: string;
}

// Enhanced D-Day Local Storage Utilities
export const dDayStorage = {
  // Storage key
  getStorageKey: (userId: string) => `dday_exams_v2_${userId}`,

  // Validation helper
  validateExam: (exam: Partial<DDayExam>): string[] => {
    const errors: string[] = [];
    if (!exam.name?.trim()) errors.push('Name is required');
    if (!exam.date) errors.push('Date is required');
    if (!exam.time) errors.push('Time is required');
    if (!exam.userId) errors.push('User ID is required');

    // Validate date format
    if (exam.date && !/^\d{4}-\d{2}-\d{2}$/.test(exam.date)) {
      errors.push('Invalid date format (YYYY-MM-DD required)');
    }

    // Validate time format
    if (exam.time && !/^\d{2}:\d{2}$/.test(exam.time)) {
      errors.push('Invalid time format (HH:MM required)');
    }

    return errors;
  },

  // Get all D-Day exams for a user
  getAll: (userId: string): DDayExam[] => {
    try {
      const data = localStorage.getItem(dDayStorage.getStorageKey(userId));
      const exams = data ? JSON.parse(data) : [];

      // Migrate old format if needed
      return exams.map((exam: any) => dDayStorage.migrateExamFormat(exam));
    } catch (error) {
      console.error('Error loading D-Day exams:', error);
      return [];
    }
  },

  // Migrate exam to new format
  migrateExamFormat: (exam: any): DDayExam => {
    // Handle migration from old format with completedTopics/confidenceLevel to new chapter-based format
    let chapters: ChapterProgress[] = [];

    if (exam.preparationData?.chapters) {
      // Already in new format
      chapters = exam.preparationData.chapters;
    } else if (exam.preparationData?.totalTopics) {
      // Migrate from old format - convert topics to chapters
      chapters = exam.preparationData.totalTopics.map((topic: string) => ({
        chapterId: `chapter-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        chapterName: topic,
        subject: '',
        isCompleted: exam.preparationData.completedTopics?.includes(topic) || false,
        completionPercentage: exam.preparationData.completedTopics?.includes(topic) ? 100 : 0,
        confidenceLevel: exam.preparationData.confidenceLevel || 3,
      }));
    }

    return {
      id: exam.id,
      name: exam.name,
      date: exam.date,
      time: exam.time || '09:00',
      userId: exam.userId,
      status: exam.status || 'upcoming',
      priority: exam.priority || 'medium',
      category: exam.category,
      description: exam.description,
      reminderSettings: exam.reminderSettings || {
        enabled: true,
        intervals: [7, 3, 1], // 1 week, 3 days, 1 day before
      },
      preparationData: {
        chapters,
        totalTopics: exam.preparationData?.totalTopics || [], // Keep for backward compatibility
        lastStudySession: exam.preparationData?.lastStudySession,
      },
      syncedFromUpcoming: exam.syncedFromUpcoming || false,
      createdAt: exam.createdAt || new Date().toISOString(),
      updatedAt: exam.updatedAt || new Date().toISOString(),
    };
  },

  // Get upcoming D-Day exams (within specified days)
  getUpcoming: (userId: string, daysAhead: number = 30): DDayExam[] => {
    const allExams = dDayStorage.getAll(userId);
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + daysAhead);

    return allExams
      .filter(exam => {
        const examDate = new Date(exam.date);
        return examDate >= now && examDate <= futureDate && exam.status !== 'completed';
      })
      .sort((a, b) => {
        // Sort by priority first, then by date
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      });
  },

  // Get exam by ID
  getById: (userId: string, examId: string): DDayExam | null => {
    const exams = dDayStorage.getAll(userId);
    return exams.find(exam => exam.id === examId) || null;
  },

  // Save exam with validation
  save: (userId: string, exam: DDayExam): DDayExam => {
    const errors = dDayStorage.validateExam(exam);
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }

    const exams = dDayStorage.getAll(userId);
    const existingIndex = exams.findIndex(e => e.id === exam.id);

    const examToSave = {
      ...exam,
      updatedAt: new Date().toISOString()
    };

    if (existingIndex >= 0) {
      exams[existingIndex] = examToSave;
    } else {
      exams.push(examToSave);
    }

    try {
      localStorage.setItem(dDayStorage.getStorageKey(userId), JSON.stringify(exams));
      return examToSave;
    } catch (error) {
      console.error('Error saving D-Day exam:', error);
      throw new Error('Failed to save exam to local storage');
    }
  },

  // Update exam with validation
  update: (userId: string, examId: string, updates: Partial<DDayExam>): DDayExam | null => {
    const exams = dDayStorage.getAll(userId);
    const examIndex = exams.findIndex(e => e.id === examId);

    if (examIndex === -1) return null;

    const updatedExam = {
      ...exams[examIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    const errors = dDayStorage.validateExam(updatedExam);
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }

    exams[examIndex] = updatedExam;

    try {
      localStorage.setItem(dDayStorage.getStorageKey(userId), JSON.stringify(exams));
      return updatedExam;
    } catch (error) {
      console.error('Error updating D-Day exam:', error);
      throw new Error('Failed to update exam in local storage');
    }
  },

  // Delete exam
  delete: (userId: string, examId: string): boolean => {
    const exams = dDayStorage.getAll(userId);
    const filteredExams = exams.filter(exam => exam.id !== examId);

    if (filteredExams.length === exams.length) return false;

    try {
      localStorage.setItem(dDayStorage.getStorageKey(userId), JSON.stringify(filteredExams));
      return true;
    } catch (error) {
      console.error('Error deleting D-Day exam:', error);
      return false;
    }
  },

  // Clear all exams for a user
  clear: (userId: string): void => {
    try {
      localStorage.removeItem(dDayStorage.getStorageKey(userId));
    } catch (error) {
      console.error('Error clearing D-Day exams:', error);
    }
  },

  // Get exams by priority
  getByPriority: (userId: string, priority: DDayExam['priority']): DDayExam[] => {
    return dDayStorage.getAll(userId).filter(exam => exam.priority === priority);
  },

  // Get exams by status
  getByStatus: (userId: string, status: DDayExam['status']): DDayExam[] => {
    return dDayStorage.getAll(userId).filter(exam => exam.status === status);
  },

  // Update preparation data
  updatePreparation: (userId: string, examId: string, preparationData: Partial<DDayExam['preparationData']>): DDayExam | null => {
    const exam = dDayStorage.getById(userId, examId);
    if (!exam) return null;

    const updatedPreparation = {
      ...exam.preparationData,
      ...preparationData,
    };

    return dDayStorage.update(userId, examId, { preparationData: updatedPreparation });
  },

  // Get statistics
  getStats: (userId: string) => {
    const exams = dDayStorage.getAll(userId);
    const upcoming = exams.filter(e => e.status === 'upcoming');
    const completed = exams.filter(e => e.status === 'completed');
    const missed = exams.filter(e => e.status === 'missed');

    return {
      total: exams.length,
      upcoming: upcoming.length,
      completed: completed.length,
      missed: missed.length,
      byPriority: {
        critical: exams.filter(e => e.priority === 'critical').length,
        high: exams.filter(e => e.priority === 'high').length,
        medium: exams.filter(e => e.priority === 'medium').length,
        low: exams.filter(e => e.priority === 'low').length,
      },
      averageStudyHours: 0, // Will be calculated from study sessions
    };
  }
};

// Study Hours Calculation Utilities
export const studyHoursCalculator = {
  // Calculate total study hours for an exam from study sessions
  calculateStudyHours: async (examId: string, userId: string, examDate: string, subjects?: string[]): Promise<number> => {
    try {
      // Calculate date range - from 30 days before exam to exam date
      const examDateObj = new Date(examDate);
      const startDate = new Date(examDateObj);
      startDate.setDate(startDate.getDate() - 30);

      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = examDate;

      // Get study sessions for the date range
      const sessions = await getStudySessions(userId, startDateStr, endDateStr);

      // Filter sessions by subjects if provided
      let filteredSessions = sessions;
      if (subjects && subjects.length > 0) {
        filteredSessions = sessions.filter(session =>
          subjects.some(subject =>
            session.subject?.toLowerCase().includes(subject.toLowerCase()) ||
            subject.toLowerCase().includes(session.subject?.toLowerCase() || '')
          )
        );
      }

      // Calculate total duration in hours
      const totalSeconds = filteredSessions.reduce((sum, session) => sum + (session.duration || 0), 0);
      return Math.round((totalSeconds / 3600) * 10) / 10; // Convert to hours and round to 1 decimal
    } catch (error) {
      console.error('Error calculating study hours:', error);
      return 0;
    }
  },

  // Calculate study hours for multiple exams
  calculateMultipleExamStudyHours: async (exams: DDayExam[], userId: string): Promise<Record<string, number>> => {
    const results: Record<string, number> = {};

    for (const exam of exams) {
      // Extract subjects from exam chapters or topics
      const subjects = exam.preparationData.chapters.map(ch => ch.chapterName);
      results[exam.id] = await studyHoursCalculator.calculateStudyHours(
        exam.id,
        userId,
        exam.date,
        subjects.length > 0 ? subjects : exam.preparationData.totalTopics
      );
    }

    return results;
  }
};

// Enhanced D-Day Integration Utilities
export const dDayIntegration = {
  // Convert upcoming test to enhanced D-Day exam format
  convertToExam: (upcomingTest: UpcomingTest): DDayExam => {
    // Convert syllabus topics to chapters with default values
    const chapters: ChapterProgress[] = (upcomingTest.syllabus?.chapters || []).map(chapter => ({
      chapterId: chapter.id,
      chapterName: chapter.name,
      subject: '',
      isCompleted: chapter.completed,
      notes: chapter.notes,
      completedAt: chapter.completed ? new Date().toISOString() : undefined,
      completionPercentage: 0, // Start with 0% completion
      confidenceLevel: 3 as const, // Default medium confidence
    }));

    return {
      id: upcomingTest.id,
      name: upcomingTest.name,
      date: upcomingTest.date,
      time: upcomingTest.time || '09:00',
      userId: upcomingTest.userId,
      status: 'upcoming' as const,
      priority: 'medium', // Use default priority instead of auto-calculating
      category: upcomingTest.categoryId,
      description: upcomingTest.description || `Synced from upcoming test: ${upcomingTest.name}`,
      reminderSettings: {
        enabled: upcomingTest.isNotificationEnabled,
        intervals: [7, 3, 1], // Default reminder intervals
      },
      preparationData: {
        chapters,
        totalTopics: upcomingTest.syllabus?.topics || [], // Keep for backward compatibility
      },
      syncedFromUpcoming: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  },

  // Determine priority based on how close the exam is
  determinePriority: (examDate: string): DDayExam['priority'] => {
    const now = new Date();
    const exam = new Date(examDate);
    const daysUntil = Math.ceil((exam.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (daysUntil <= 1) return 'critical';
    if (daysUntil <= 3) return 'high';
    if (daysUntil <= 7) return 'medium';
    return 'low';
  },

  // Get upcoming tests that should appear in D-Day with enhanced logic
  getUpcomingForDDay: (userId: string, daysAhead: number = 30): DDayExam[] => {
    try {
      // Get from both upcoming tests and direct D-Day exams
      const upcomingTests = upcomingTestStorage.getUpcoming(userId, daysAhead);
      const dDayExams = dDayStorage.getUpcoming(userId, daysAhead);

      // Convert upcoming tests to D-Day format
      const convertedTests = upcomingTests.map(test => dDayIntegration.convertToExam(test));

      // Combine and deduplicate (D-Day exams take precedence)
      const allExams = [...dDayExams];
      convertedTests.forEach(convertedTest => {
        const existingExam = allExams.find(exam => exam.id === convertedTest.id);
        if (!existingExam) {
          allExams.push(convertedTest);
        } else if (existingExam.syncedFromUpcoming) {
          // Update synced exam with latest data from upcoming test
          const updatedExam = {
            ...existingExam,
            ...convertedTest,
            // Preserve user customizations
            priority: existingExam.priority,
            reminderSettings: existingExam.reminderSettings,
            preparationData: existingExam.preparationData,
          };
          const index = allExams.findIndex(exam => exam.id === convertedTest.id);
          allExams[index] = updatedExam;
        }
      });

      return allExams.sort((a, b) => {
        // Sort by priority first, then by date
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      });
    } catch (error) {
      console.error('Error getting upcoming exams for D-Day:', error);
      return [];
    }
  },

  // Enhanced sync with better error handling and validation
  syncWithDDay: (upcomingTest: UpcomingTest): DDayExam => {
    try {
      const examData = dDayIntegration.convertToExam(upcomingTest);

      // Check if exam already exists and preserve user customizations
      const existingExam = dDayStorage.getById(upcomingTest.userId, upcomingTest.id);
      if (existingExam && existingExam.syncedFromUpcoming) {
        // Preserve user customizations while updating core data
        const updatedExam = {
          ...examData,
          priority: existingExam.priority,
          reminderSettings: existingExam.reminderSettings,
          preparationData: existingExam.preparationData,
        };
        return dDayStorage.save(upcomingTest.userId, updatedExam);
      }

      return dDayStorage.save(upcomingTest.userId, examData);
    } catch (error) {
      console.error('Error syncing with D-Day:', error);
      throw new Error('Failed to sync upcoming test with D-Day');
    }
  },

  // Remove D-Day exam when upcoming test is deleted
  removeDDayExam: (userId: string, testId: string): boolean => {
    try {
      const exam = dDayStorage.getById(userId, testId);

      // We only want to delete the D-Day exam if it was synced from
      // the upcoming test list. If it's a standalone D-Day exam,
      // it should not be removed.
      if (exam && exam.syncedFromUpcoming) {
        return dDayStorage.delete(userId, testId);
      }
      
      // If the exam doesn't exist in D-Day storage or wasn't synced,
      // there's nothing to do, so we return true to indicate success.
      return true;
    } catch (error) {
      console.error('Error removing D-Day exam:', error);
      return false;
    }
  },

  // Bulk sync multiple upcoming tests
  bulkSyncWithDDay: (upcomingTests: UpcomingTest[]): { success: DDayExam[], failed: string[] } => {
    const success: DDayExam[] = [];
    const failed: string[] = [];

    upcomingTests.forEach(test => {
      try {
        const synced = dDayIntegration.syncWithDDay(test);
        success.push(synced);
      } catch (error) {
        failed.push(test.id);
        console.error(`Failed to sync test ${test.name}:`, error);
      }
    });

    return { success, failed };
  },

  // Check sync status between upcoming tests and D-Day
  getSyncStatus: (userId: string): { synced: number, unsynced: number, conflicts: number } => {
    try {
      const upcomingTests = upcomingTestStorage.getAll(userId);
      const dDayExams = dDayStorage.getAll(userId);

      let synced = 0;
      let unsynced = 0;
      let conflicts = 0;

      upcomingTests.forEach(test => {
        const dDayExam = dDayExams.find(exam => exam.id === test.id);
        if (dDayExam) {
          if (dDayExam.syncedFromUpcoming) {
            synced++;
          } else {
            conflicts++;
          }
        } else {
          unsynced++;
        }
      });

      return { synced, unsynced, conflicts };
    } catch (error) {
      console.error('Error checking sync status:', error);
      return { synced: 0, unsynced: 0, conflicts: 0 };
    }
  }
};

// Chapter Progress Utilities
export const chapterProgressUtils = {
  // Calculate overall preparation percentage for an exam
  calculateOverallProgress: (chapters: ChapterProgress[]): number => {
    if (chapters.length === 0) return 0;

    // Weight completion percentage and confidence level
    const totalScore = chapters.reduce((sum, chapter) => {
      // Completion percentage (0-100) weighted at 70%
      const completionScore = chapter.completionPercentage * 0.7;

      // Confidence level (1-5) converted to percentage and weighted at 30%
      const confidenceScore = ((chapter.confidenceLevel - 1) / 4) * 100 * 0.3;

      return sum + (completionScore + confidenceScore);
    }, 0);

    return Math.round(totalScore / chapters.length);
  },

  // Get completion status for display
  getCompletionStatus: (chapters: ChapterProgress[]): {
    completed: number;
    total: number;
    percentage: number;
  } => {
    const total = chapters.length;
    const completed = chapters.filter(chapter => chapter.completionPercentage >= 80).length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    return { completed, total, percentage };
  },

  // Get confidence distribution
  getConfidenceDistribution: (chapters: ChapterProgress[]): {
    veryLow: number;
    low: number;
    medium: number;
    high: number;
    veryHigh: number;
  } => {
    const distribution = {
      veryLow: 0,
      low: 0,
      medium: 0,
      high: 0,
      veryHigh: 0,
    };

    chapters.forEach(chapter => {
      switch (chapter.confidenceLevel) {
        case 1: distribution.veryLow++; break;
        case 2: distribution.low++; break;
        case 3: distribution.medium++; break;
        case 4: distribution.high++; break;
        case 5: distribution.veryHigh++; break;
      }
    });

    return distribution;
  },

  // Get chapters that need attention (low completion or confidence)
  getChaptersNeedingAttention: (chapters: ChapterProgress[]): ChapterProgress[] => {
    return chapters.filter(chapter =>
      chapter.completionPercentage < 50 || chapter.confidenceLevel <= 2
    );
  },

  // Update chapter progress
  updateChapterProgress: (
    chapters: ChapterProgress[],
    chapterName: string,
    updates: Partial<Pick<ChapterProgress, 'completionPercentage' | 'confidenceLevel'>>
  ): ChapterProgress[] => {
    return chapters.map(chapter =>
      chapter.chapterName === chapterName
        ? { ...chapter, ...updates }
        : chapter
    );
  },

  // Add new chapter
  addChapter: (chapters: ChapterProgress[], chapterName: string): ChapterProgress[] => {
    if (chapters.some(chapter => chapter.chapterName === chapterName)) {
      return chapters; // Chapter already exists
    }

    return [...chapters, {
      chapterId: `chapter-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      chapterName,
      subject: '',
      isCompleted: false,
      completionPercentage: 0,
      confidenceLevel: 3,
    }];
  },

  // Remove chapter
  removeChapter: (chapters: ChapterProgress[], chapterName: string): ChapterProgress[] => {
    return chapters.filter(chapter => chapter.chapterName !== chapterName);
  },
};
