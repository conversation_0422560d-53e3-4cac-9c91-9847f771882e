import { SWOTItem, SWOTAnalysis } from '@/types/dashboard';

// Local storage key for SWOT analysis data
const SWOT_STORAGE_KEY = 'swot-analysis';

// Default SWOT analysis structure
const createDefaultSWOTAnalysis = (userId: string): SWOTAnalysis => ({
  strengths: [],
  weaknesses: [],
  opportunities: [],
  threats: [],
  lastUpdated: new Date().toISOString(),
  userId
});

// SWOT Storage utilities
export const swotStorage = {
  // Get SWOT analysis for a user
  getSWOTAnalysis: (userId: string): SWOTAnalysis => {
    try {
      const stored = localStorage.getItem(SWOT_STORAGE_KEY);
      if (!stored) {
        return createDefaultSWOTAnalysis(userId);
      }

      const data = JSON.parse(stored);
      
      // Check if data exists for this user
      if (data[userId]) {
        return data[userId];
      }

      return createDefaultSWOTAnalysis(userId);
    } catch (error) {
      console.error('Error loading SWOT analysis:', error);
      return createDefaultSWOTAnalysis(userId);
    }
  },

  // Save SWOT analysis for a user
  saveSWOTAnalysis: (userId: string, analysis: SWOTAnalysis): void => {
    try {
      const stored = localStorage.getItem(SWOT_STORAGE_KEY);
      const data = stored ? JSON.parse(stored) : {};
      
      data[userId] = {
        ...analysis,
        lastUpdated: new Date().toISOString()
      };

      localStorage.setItem(SWOT_STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving SWOT analysis:', error);
    }
  },

  // Add a new SWOT item
  addSWOTItem: (userId: string, item: Omit<SWOTItem, 'id' | 'createdAt' | 'updatedAt' | 'userId'>): SWOTItem => {
    const analysis = swotStorage.getSWOTAnalysis(userId);
    
    const newItem: SWOTItem = {
      ...item,
      id: `swot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId
    };

    // Add to appropriate array
    switch (item.type) {
      case 'strength':
        analysis.strengths.push(newItem);
        break;
      case 'weakness':
        analysis.weaknesses.push(newItem);
        break;
      case 'opportunity':
        analysis.opportunities.push(newItem);
        break;
      case 'threat':
        analysis.threats.push(newItem);
        break;
    }

    swotStorage.saveSWOTAnalysis(userId, analysis);
    return newItem;
  },

  // Update a SWOT item
  updateSWOTItem: (userId: string, itemId: string, updates: Partial<SWOTItem>): boolean => {
    const analysis = swotStorage.getSWOTAnalysis(userId);
    let found = false;

    // Update in all arrays
    [analysis.strengths, analysis.weaknesses, analysis.opportunities, analysis.threats].forEach(array => {
      const index = array.findIndex(item => item.id === itemId);
      if (index !== -1) {
        array[index] = {
          ...array[index],
          ...updates,
          updatedAt: new Date().toISOString()
        };
        found = true;
      }
    });

    if (found) {
      swotStorage.saveSWOTAnalysis(userId, analysis);
    }

    return found;
  },

  // Delete a SWOT item
  deleteSWOTItem: (userId: string, itemId: string): boolean => {
    const analysis = swotStorage.getSWOTAnalysis(userId);
    let found = false;

    // Remove from all arrays
    ['strengths', 'weaknesses', 'opportunities', 'threats'].forEach(key => {
      const array = analysis[key as keyof SWOTAnalysis] as SWOTItem[];
      const index = array.findIndex(item => item.id === itemId);
      if (index !== -1) {
        array.splice(index, 1);
        found = true;
      }
    });

    if (found) {
      swotStorage.saveSWOTAnalysis(userId, analysis);
    }

    return found;
  },

  // Get items by type
  getItemsByType: (userId: string, type: SWOTItem['type']): SWOTItem[] => {
    const analysis = swotStorage.getSWOTAnalysis(userId);
    
    switch (type) {
      case 'strength':
        return analysis.strengths;
      case 'weakness':
        return analysis.weaknesses;
      case 'opportunity':
        return analysis.opportunities;
      case 'threat':
        return analysis.threats;
      default:
        return [];
    }
  },

  // Get SWOT summary statistics
  getSWOTSummary: (userId: string) => {
    const analysis = swotStorage.getSWOTAnalysis(userId);
    
    return {
      totalItems: analysis.strengths.length + analysis.weaknesses.length + 
                  analysis.opportunities.length + analysis.threats.length,
      strengthsCount: analysis.strengths.length,
      weaknessesCount: analysis.weaknesses.length,
      opportunitiesCount: analysis.opportunities.length,
      threatsCount: analysis.threats.length,
      lastUpdated: analysis.lastUpdated,
      categories: {
        academic: [analysis.strengths, analysis.weaknesses, analysis.opportunities, analysis.threats]
          .flat()
          .filter(item => item.category.toLowerCase().includes('academic')).length,
        personal: [analysis.strengths, analysis.weaknesses, analysis.opportunities, analysis.threats]
          .flat()
          .filter(item => item.category.toLowerCase().includes('personal')).length,
        technical: [analysis.strengths, analysis.weaknesses, analysis.opportunities, analysis.threats]
          .flat()
          .filter(item => item.category.toLowerCase().includes('technical')).length
      }
    };
  },

  // Clear all SWOT data for a user
  clearSWOTAnalysis: (userId: string): void => {
    try {
      const stored = localStorage.getItem(SWOT_STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        delete data[userId];
        localStorage.setItem(SWOT_STORAGE_KEY, JSON.stringify(data));
      }
    } catch (error) {
      console.error('Error clearing SWOT analysis:', error);
    }
  },

  // Export SWOT analysis as JSON
  exportSWOTAnalysis: (userId: string): string => {
    const analysis = swotStorage.getSWOTAnalysis(userId);
    return JSON.stringify(analysis, null, 2);
  },

  // Import SWOT analysis from JSON
  importSWOTAnalysis: (userId: string, jsonData: string): boolean => {
    try {
      const analysis = JSON.parse(jsonData) as SWOTAnalysis;
      
      // Validate structure
      if (!analysis.strengths || !analysis.weaknesses || !analysis.opportunities || !analysis.threats) {
        throw new Error('Invalid SWOT analysis structure');
      }

      // Update user ID and timestamps
      analysis.userId = userId;
      analysis.lastUpdated = new Date().toISOString();

      swotStorage.saveSWOTAnalysis(userId, analysis);
      return true;
    } catch (error) {
      console.error('Error importing SWOT analysis:', error);
      return false;
    }
  }
};

// SWOT categories for dropdown/selection
export const SWOT_CATEGORIES = [
  'Academic',
  'Personal',
  'Technical',
  'Social',
  'Career',
  'Health',
  'Financial',
  'Creative',
  'Leadership',
  'Communication'
];

// SWOT type colors for UI
export const SWOT_TYPE_COLORS = {
  strength: {
    bg: 'bg-emerald-500/20',
    border: 'border-emerald-500/30',
    text: 'text-emerald-600',
    icon: 'text-emerald-500'
  },
  weakness: {
    bg: 'bg-rose-500/20',
    border: 'border-rose-500/30',
    text: 'text-rose-600',
    icon: 'text-rose-500'
  },
  opportunity: {
    bg: 'bg-violet-500/20',
    border: 'border-violet-500/30',
    text: 'text-violet-600',
    icon: 'text-violet-500'
  },
  threat: {
    bg: 'bg-orange-500/20',
    border: 'border-orange-500/30',
    text: 'text-orange-600',
    icon: 'text-orange-500'
  }
};
