import { ReactNode } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface DashboardCardProps {
  title: string;
  icon?: LucideIcon;
  iconColor?: string;
  children: ReactNode;
  className?: string;
  headerAction?: ReactNode;
  isLoading?: boolean;
  error?: string | null;
  onClick?: () => void;
  size?: "small" | "medium" | "large";
}

export function DashboardCard({
  title,
  icon: Icon,
  iconColor = "text-violet-500",
  children,
  className,
  headerAction,
  isLoading = false,
  error = null,
  onClick,
  size = "medium"
}: DashboardCardProps) {
  const sizeClasses = {
    small: "min-h-[200px]",
    medium: "min-h-[280px]",
    large: "min-h-[320px]"
  };

  const cardContent = (
    <Card 
      className={cn(
        "bg-background/30 dark:bg-background/20 backdrop-blur-md border border-border/50 dark:border-white/10 shadow-lg dark:shadow-2xl dark:shadow-violet-500/5 transition-all duration-300 hover:shadow-xl dark:hover:shadow-violet-500/10 hover:border-border dark:hover:border-white/20",
        sizeClasses[size],
        onClick && "cursor-pointer hover:scale-[1.02]",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {Icon && (
              <div className={cn(
                "p-2 rounded-lg bg-gradient-to-br from-violet-500/20 to-purple-500/20 border border-violet-500/30",
                iconColor
              )}>
                <Icon className="h-5 w-5" />
              </div>
            )}
            <span className="text-lg font-semibold text-foreground">
              {title}
            </span>
          </div>
          {headerAction && (
            <div className="flex items-center gap-2">
              {headerAction}
            </div>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pt-0">
        {error ? (
          <div className="flex items-center justify-center h-32 text-center">
            <div className="text-destructive">
              <p className="text-sm font-medium">Error loading data</p>
              <p className="text-xs text-muted-foreground mt-1">{error}</p>
            </div>
          </div>
        ) : isLoading ? (
          <div className="space-y-3">
            {/* Loading skeleton */}
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
              <div className="h-8 bg-muted rounded w-full"></div>
              <div className="flex gap-2">
                <div className="h-6 bg-muted rounded w-16"></div>
                <div className="h-6 bg-muted rounded w-20"></div>
              </div>
            </div>
          </div>
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={onClick ? { y: -2 } : undefined}
      className="h-full"
    >
      {cardContent}
    </motion.div>
  );
}

// Loading skeleton component for consistent loading states
export function DashboardCardSkeleton({ 
  size = "medium" 
}: { 
  size?: "small" | "medium" | "large" 
}) {
  const sizeClasses = {
    small: "min-h-[200px]",
    medium: "min-h-[280px]",
    large: "min-h-[320px]"
  };

  return (
    <Card className={cn(
      "bg-background/30 dark:bg-background/20 backdrop-blur-md border border-border/50 dark:border-white/10",
      sizeClasses[size]
    )}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-3">
          <div className="w-9 h-9 bg-muted rounded-lg animate-pulse"></div>
          <div className="h-5 bg-muted rounded w-32 animate-pulse"></div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="h-8 bg-muted rounded w-full"></div>
          <div className="flex gap-2">
            <div className="h-6 bg-muted rounded w-16"></div>
            <div className="h-6 bg-muted rounded w-20"></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Error card component for consistent error states
export function DashboardCardError({ 
  title, 
  error, 
  onRetry,
  size = "medium" 
}: { 
  title: string;
  error: string;
  onRetry?: () => void;
  size?: "small" | "medium" | "large";
}) {
  const sizeClasses = {
    small: "min-h-[200px]",
    medium: "min-h-[280px]",
    large: "min-h-[320px]"
  };

  return (
    <Card className={cn(
      "bg-background/30 dark:bg-background/20 backdrop-blur-md border border-destructive/50",
      sizeClasses[size]
    )}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-foreground">
          {title}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex flex-col items-center justify-center h-32 text-center space-y-3">
          <div className="text-destructive">
            <p className="text-sm font-medium">Failed to load data</p>
            <p className="text-xs text-muted-foreground mt-1">{error}</p>
          </div>
          {onRetry && (
            <button
              onClick={onRetry}
              className="text-xs text-violet-500 hover:text-violet-400 underline"
            >
              Try again
            </button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
