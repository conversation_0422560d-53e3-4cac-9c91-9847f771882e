import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardCard } from "@/components/shared/DashboardCard";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { dDayStorage, DDayExam, ChapterProgress } from "@/utils/mockTestLocalStorage";
import {
  BookOpen,
  CheckCircle2,
  Circle,
  TrendingUp,
  Calendar,
  Target
} from "lucide-react";
import { cn } from "@/lib/utils";
import { parseISO, differenceInDays } from "date-fns";

interface SubjectProgress {
  subject: string;
  completed: number;
  total: number;
  percentage: number;
  chapters: ChapterProgress[];
  examName: string;
  examDate: string;
  daysLeft: number;
}

export function ChapterProgressCard() {
  const navigate = useNavigate();
  const { user } = useSupabaseAuth();
  const [subjectProgress, setSubjectProgress] = useState<SubjectProgress[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadChapterProgress();
    }
  }, [user]);

  const loadChapterProgress = () => {
    if (!user) return;

    setLoading(true);
    try {
      const allExams = dDayStorage.getAllExams(user.id);

      // Get upcoming exams with chapters
      const upcomingExams = allExams
        .filter(exam => exam.status === 'upcoming')
        .filter(exam => {
          const examDateTime = parseISO(`${exam.date}T${exam.time}`);
          return !examDateTime || examDateTime > new Date();
        })
        .filter(exam => exam.preparationData?.chapters && exam.preparationData.chapters.length > 0);

      // Group chapters by subject across all exams
      const subjectMap: { [key: string]: SubjectProgress } = {};

      upcomingExams.forEach(exam => {
        if (!exam.preparationData?.chapters) return;

        exam.preparationData.chapters.forEach(chapter => {
          const subject = chapter.subject || 'General';

          if (!subjectMap[subject]) {
            subjectMap[subject] = {
              subject,
              completed: 0,
              total: 0,
              percentage: 0,
              chapters: [],
              examName: exam.name,
              examDate: exam.date,
              daysLeft: differenceInDays(parseISO(`${exam.date}T${exam.time}`), new Date())
            };
          }

          subjectMap[subject].chapters.push(chapter);
          subjectMap[subject].total++;
          if (chapter.isCompleted) {
            subjectMap[subject].completed++;
          }
        });
      });

      // Calculate percentages and sort by progress
      const progressArray = Object.values(subjectMap).map(subject => ({
        ...subject,
        percentage: subject.total > 0 ? Math.round((subject.completed / subject.total) * 100) : 0
      })).sort((a, b) => b.percentage - a.percentage);

      setSubjectProgress(progressArray.slice(0, 6)); // Show max 6 subjects
    } catch (error) {
      console.error('Error loading chapter progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "text-emerald-400";
    if (percentage >= 60) return "text-blue-400";
    if (percentage >= 40) return "text-yellow-400";
    return "text-rose-400";
  };

  const getProgressBg = (percentage: number) => {
    if (percentage >= 80) return "bg-emerald-500/20 border-emerald-500/30";
    if (percentage >= 60) return "bg-blue-500/20 border-blue-500/30";
    if (percentage >= 40) return "bg-yellow-500/20 border-yellow-500/30";
    return "bg-rose-500/20 border-rose-500/30";
  };

  const headerAction = (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => navigate("/mock-tests")}
      className="text-xs hover:bg-blue-500/20 hover:text-blue-400"
    >
      Manage
    </Button>
  );

  return (
    <DashboardCard
      title="Chapter Progress"
      icon={BookOpen}
      iconColor="text-blue-500"
      size="large"
      headerAction={headerAction}
      isLoading={loading}
    >
      <div className="space-y-4">
        {subjectProgress.length === 0 ? (
          <div className="text-center py-8">
            <BookOpen className="h-12 w-12 text-blue-500/50 mx-auto mb-3" />
            <p className="text-muted-foreground">No chapter progress to track</p>
            <p className="text-sm text-muted-foreground/70 mb-4">Add exams with syllabus to track your preparation</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/mock-tests")}
              className="border-blue-500/30 hover:bg-blue-500/10 text-blue-400"
            >
              <Target className="h-4 w-4 mr-2" />
              Add Exam
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {subjectProgress.map((subject, index) => (
              <div
                key={`${subject.subject}-${index}`}
                className={cn(
                  "p-4 rounded-lg border transition-all duration-200 hover:bg-background/50",
                  getProgressBg(subject.percentage)
                )}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-sm truncate mb-1">{subject.subject}</h4>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{subject.examName}</span>
                      {subject.daysLeft > 0 && (
                        <Badge variant="outline" className="text-xs px-2 py-0">
                          {subject.daysLeft} days left
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className={cn("text-right", getProgressColor(subject.percentage))}>
                    <p className="text-lg font-bold leading-none">
                      {subject.percentage}%
                    </p>
                    <p className="text-xs">
                      {subject.completed}/{subject.total}
                    </p>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <Progress value={subject.percentage} className="h-2" />

                  {/* Chapter Details */}
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1 text-emerald-400">
                        <CheckCircle2 className="h-3 w-3" />
                        <span>{subject.completed} completed</span>
                      </div>
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Circle className="h-3 w-3" />
                        <span>{subject.total - subject.completed} remaining</span>
                      </div>
                    </div>

                    {subject.percentage > 0 && (
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <TrendingUp className="h-3 w-3" />
                        <span>On track</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {subjectProgress.length > 0 && (
          <div className="pt-2 border-t border-border/50">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Overall Progress</span>
              <span className="font-medium">
                {Math.round(
                  subjectProgress.reduce((acc, subject) => acc + subject.percentage, 0) /
                  subjectProgress.length
                )}% average
              </span>
            </div>
          </div>
        )}
      </div>
    </DashboardCard>
  );
}
