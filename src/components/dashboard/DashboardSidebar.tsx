import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  <PERSON><PERSON>ip,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  LayoutDashboard,
  Bot,
  Users,
  Timer,
  BarChart3,
  CheckSquare,
  FileText,
  Menu,
  ChevronLeft,
  ChevronRight,
  Home
} from "lucide-react";

interface NavItem {
  title: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  badge?: string;
}

const navigationItems: NavItem[] = [
  {
    title: "Dashboard",
    path: "/dashboard",
    icon: LayoutDashboard,
    description: "Overview of your productivity"
  },
  {
    title: "AI Assistant",
    path: "/ai",
    icon: Bo<PERSON>,
    description: "Get help with your studies"
  },
  {
    title: "Study Groups",
    path: "/groups",
    icon: Users,
    description: "Collaborate with peers"
  },
  {
    title: "Productivity",
    path: "/productivity",
    icon: Timer,
    description: "Focus timer and tools"
  },
  {
    title: "Analytics",
    path: "/analytics",
    icon: BarChart3,
    description: "Track your progress"
  },
  {
    title: "Tasks",
    path: "/tasks",
    icon: CheckSquare,
    description: "Manage your todos"
  },
  {
    title: "Mock Tests",
    path: "/mock-tests",
    icon: FileText,
    description: "Practice and analyze"
  }
];

export function DashboardSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const location = useLocation();

  // Auto-collapse on mobile and handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsCollapsed(true);
        setIsMobileOpen(false); // Close mobile menu on resize
      } else {
        // On desktop, restore expanded state if needed
        const savedCollapsed = localStorage.getItem('dashboard-sidebar-collapsed');
        setIsCollapsed(savedCollapsed === 'true');
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Save collapsed state to localStorage
  useEffect(() => {
    if (window.innerWidth >= 768) {
      localStorage.setItem('dashboard-sidebar-collapsed', isCollapsed.toString());
    }
  }, [isCollapsed]);

  const SidebarContent = ({ isMobile = false }: { isMobile?: boolean }) => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-border/50">
        <Link 
          to="/" 
          className="flex items-center gap-3 group"
          onClick={() => isMobile && setIsMobileOpen(false)}
        >
          <div className="relative w-8 h-8">
            <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 to-indigo-500/20 rounded-full blur-md group-hover:opacity-100 opacity-0 transition-opacity"></div>
            <img
              src="/icon-192x192.png"
              alt="IsotopeAI Logo"
              className="w-full h-full rounded-full border border-border dark:border-white/10 shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300"
            />
          </div>
          <AnimatePresence>
            {(!isCollapsed || isMobile) && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                className="font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r from-violet-400 to-indigo-400 whitespace-nowrap"
              >
                IsotopeAI
              </motion.span>
            )}
          </AnimatePresence>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const isActive = location.pathname === item.path;
          const Icon = item.icon;

          const navItem = (
            <Link
              key={item.path}
              to={item.path}
              onClick={() => isMobile && setIsMobileOpen(false)}
              className={cn(
                "flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 group relative",
                isActive
                  ? "bg-violet-500/20 text-violet-400 border border-violet-500/30"
                  : "hover:bg-background/50 text-muted-foreground hover:text-foreground"
              )}
            >
              {/* Active indicator */}
              {isActive && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-indigo-500/10 rounded-lg border border-violet-500/20"
                  initial={false}
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
              
              <Icon className={cn(
                "h-5 w-5 relative z-10 transition-colors",
                isActive ? "text-violet-400" : "text-muted-foreground group-hover:text-foreground"
              )} />
              
              <AnimatePresence>
                {(!isCollapsed || isMobile) && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    className="flex items-center justify-between flex-1 relative z-10"
                  >
                    <span className="font-medium whitespace-nowrap">
                      {item.title}
                    </span>
                    {item.badge && (
                      <span className="bg-violet-500/20 text-violet-400 text-xs px-2 py-1 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </Link>
          );

          // Wrap with tooltip for collapsed state
          if (isCollapsed && !isMobile) {
            return (
              <TooltipProvider key={item.path}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    {navItem}
                  </TooltipTrigger>
                  <TooltipContent side="right" className="ml-2">
                    <div>
                      <p className="font-medium">{item.title}</p>
                      <p className="text-xs text-muted-foreground">{item.description}</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            );
          }

          return navItem;
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border/50">
        {!isMobile && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="w-full justify-center"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <>
                <ChevronLeft className="h-4 w-4 mr-2" />
                Collapse
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <motion.aside
        initial={false}
        animate={{ width: isCollapsed ? 80 : 256 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className="hidden md:flex fixed left-0 top-0 h-screen bg-background/30 dark:bg-background/20 backdrop-blur-md border-r border-border/50 dark:border-white/10 z-40"
      >
        <SidebarContent />
      </motion.aside>

      {/* Mobile Sidebar */}
      <div className="md:hidden">
        <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="fixed top-4 left-4 z-50 md:hidden bg-background/80 backdrop-blur-md"
            >
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent 
            side="left" 
            className="w-64 p-0 bg-background/95 backdrop-blur-md"
          >
            <SidebarContent isMobile />
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
}
