import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardCard } from "@/components/shared/DashboardCard";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { getStudySessions } from "@/utils/supabase";
import {
  BarChart3,
  Clock,
  Target,
  Flame,
  TrendingUp
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format, startOfDay, endOfDay, subDays, isToday } from "date-fns";

interface StudySession {
  subject: string;
  duration: number; // in seconds
  mode: "pomodoro" | "stopwatch";
  phase: "work" | "shortBreak" | "longBreak";
  completed: boolean;
  start_time: string;
  date: string;
}

interface DashboardAnalytics {
  todayStudyTime: number; // in minutes
  dailyTarget: number; // in minutes
  weeklyStreak: number;
  completedPomodoros: number;
  targetProgress: number; // percentage
  mostStudiedSubject: string;
}

export function StudyAnalyticsCard() {
  const navigate = useNavigate();
  const { user } = useSupabaseAuth();
  const [analytics, setAnalytics] = useState<DashboardAnalytics>({
    todayStudyTime: 0,
    dailyTarget: 120, // 2 hours default
    weeklyStreak: 0,
    completedPomodoros: 0,
    targetProgress: 0,
    mostStudiedSubject: "No data"
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadAnalytics();
    }
  }, [user]);

  const loadAnalytics = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Get study sessions for the last 7 days
      const endDate = endOfDay(new Date());
      const startDate = startOfDay(subDays(new Date(), 7));

      const sessions = await getStudySessions(user.id, startDate, endDate);

      // Calculate today's study time
      const todaySessions = sessions.filter(session =>
        isToday(new Date(session.start_time))
      );

      const todayStudyTime = todaySessions.reduce((total, session) => {
        return total + (session.duration / 60); // Convert seconds to minutes
      }, 0);

      // Calculate completed pomodoros today
      const completedPomodoros = todaySessions.filter(session =>
        session.mode === 'pomodoro' && session.completed && session.phase === 'work'
      ).length;

      // Calculate weekly streak
      let streak = 0;
      for (let i = 0; i < 7; i++) {
        const checkDate = subDays(new Date(), i);
        const dayStart = startOfDay(checkDate);
        const dayEnd = endOfDay(checkDate);

        const dayHasStudy = sessions.some(session => {
          const sessionDate = new Date(session.start_time);
          return sessionDate >= dayStart && sessionDate <= dayEnd && session.duration > 0;
        });

        if (dayHasStudy) {
          streak++;
        } else if (i > 0) { // Don't break streak on today if no study yet
          break;
        }
      }

      // Find most studied subject
      const subjectTimes: { [key: string]: number } = {};
      sessions.forEach(session => {
        if (session.subject) {
          subjectTimes[session.subject] = (subjectTimes[session.subject] || 0) + session.duration;
        }
      });

      const mostStudiedSubject = Object.keys(subjectTimes).length > 0
        ? Object.entries(subjectTimes).reduce((a, b) => a[1] > b[1] ? a : b)[0]
        : "No data";

      // Calculate target progress
      const dailyTarget = 120; // 2 hours in minutes
      const targetProgress = Math.min((todayStudyTime / dailyTarget) * 100, 100);

      setAnalytics({
        todayStudyTime: Math.round(todayStudyTime),
        dailyTarget,
        weeklyStreak: streak,
        completedPomodoros,
        targetProgress: Math.round(targetProgress),
        mostStudiedSubject
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const headerAction = (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => navigate("/analytics")}
      className="text-xs hover:bg-violet-500/20 hover:text-violet-400"
    >
      View Details
    </Button>
  );

  return (
    <DashboardCard
      title="Study Analytics"
      icon={BarChart3}
      iconColor="text-violet-500"
      size="medium"
      headerAction={headerAction}
      isLoading={loading}
    >
      <div className="space-y-4">
        {/* Daily Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-violet-400" />
              <span className="text-sm font-medium">Daily Target</span>
            </div>
            <span className="text-sm text-muted-foreground">
              {formatTime(analytics.todayStudyTime)} / {formatTime(analytics.dailyTarget)}
            </span>
          </div>
          <Progress
            value={analytics.targetProgress}
            className="h-2"
          />
          <p className="text-xs text-muted-foreground">
            {analytics.targetProgress >= 100 ? (
              <span className="text-yellow-400 flex items-center gap-1">
                <span>🎉</span> Target exceeded!
              </span>
            ) : (
              `${100 - analytics.targetProgress}% remaining`
            )}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-background/30 rounded-lg p-3 border border-border/50">
            <div className="flex items-center gap-2 mb-1">
              <Flame className="h-4 w-4 text-orange-400" />
              <span className="text-xs text-muted-foreground">Streak</span>
            </div>
            <p className="text-lg font-bold text-orange-400">
              {analytics.weeklyStreak}
              <span className="text-xs font-normal ml-1">days</span>
            </p>
          </div>

          <div className="bg-background/30 rounded-lg p-3 border border-border/50">
            <div className="flex items-center gap-2 mb-1">
              <Clock className="h-4 w-4 text-emerald-400" />
              <span className="text-xs text-muted-foreground">Pomodoros</span>
            </div>
            <p className="text-lg font-bold text-emerald-400">
              {analytics.completedPomodoros}
              <span className="text-xs font-normal ml-1">today</span>
            </p>
          </div>
        </div>

        {/* Most Studied Subject */}
        <div className="bg-background/30 rounded-lg p-3 border border-border/50">
          <div className="flex items-center gap-2 mb-1">
            <TrendingUp className="h-4 w-4 text-blue-400" />
            <span className="text-xs text-muted-foreground">Top Subject</span>
          </div>
          <p className="text-sm font-medium truncate">
            {analytics.mostStudiedSubject}
          </p>
        </div>
      </div>
    </DashboardCard>
  );
}
