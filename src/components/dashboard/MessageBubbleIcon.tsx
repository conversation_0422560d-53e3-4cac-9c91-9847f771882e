import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { MessageCircle } from "lucide-react";

export function MessageBubbleIcon() {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate("/groups");
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            className="fixed top-4 right-16 z-50"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClick}
              className="rounded-full bg-background/80 dark:bg-background/60 backdrop-blur-md border border-border/50 dark:border-white/10 shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-violet-500/20 hover:border-violet-500/30"
            >
              <MessageCircle className="h-5 w-5 text-muted-foreground hover:text-violet-400 transition-colors" />
            </Button>
          </motion.div>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="mb-2">
          <p>Discussion Groups</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
