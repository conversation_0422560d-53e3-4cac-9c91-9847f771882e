import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardCard } from "@/components/shared/DashboardCard";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useSupabaseTodoStore } from "@/stores/supabaseTodoStore";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { TodoItem } from "@/types/todo";
import {
  CheckSquare,
  Plus,
  Clock,
  AlertTriangle,
  CheckCircle2,
  Circle
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format, isToday, isPast } from "date-fns";

export function TodayTasksCard() {
  const navigate = useNavigate();
  const { user } = useSupabaseAuth();
  const { board, fetchTodos, updateTask, loading } = useSupabaseTodoStore();
  const [todayTasks, setTodayTasks] = useState<TodoItem[]>([]);

  useEffect(() => {
    if (user) {
      fetchTodos(user.id);
    }
  }, [user, fetchTodos]);

  useEffect(() => {
    // Filter tasks for today
    const allTasks = Object.values(board.tasks);
    const today = new Date();

    const filtered = allTasks.filter(task => {
      if (!task.dueDate) return false;
      const dueDate = new Date(task.dueDate);
      return isToday(dueDate) || (isPast(dueDate) && task.title); // Include overdue tasks
    });

    // Sort by priority and due date
    filtered.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority] || 1;
      const bPriority = priorityOrder[b.priority] || 1;

      if (aPriority !== bPriority) {
        return bPriority - aPriority; // High priority first
      }

      // Then by due date
      const aDate = a.dueDate ? new Date(a.dueDate) : new Date();
      const bDate = b.dueDate ? new Date(b.dueDate) : new Date();
      return aDate.getTime() - bDate.getTime();
    });

    setTodayTasks(filtered.slice(0, 5)); // Show max 5 tasks
  }, [board.tasks]);

  const handleTaskToggle = async (task: TodoItem) => {
    // Move task to completed column (column-3)
    try {
      await updateTask(task.id, {
        updatedAt: Date.now()
      });
      // Note: The actual column movement would be handled by the TodoBoard logic
    } catch (error) {
      console.error('Error updating task:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'low':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getTaskStatus = (task: TodoItem) => {
    if (!task.dueDate) return 'no-date';
    const dueDate = new Date(task.dueDate);
    if (isPast(dueDate) && !isToday(dueDate)) return 'overdue';
    if (isToday(dueDate)) return 'today';
    return 'upcoming';
  };

  const headerAction = (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => navigate("/tasks")}
      className="text-xs hover:bg-emerald-500/20 hover:text-emerald-400"
    >
      View All
    </Button>
  );

  return (
    <DashboardCard
      title="Today's Tasks"
      icon={CheckSquare}
      iconColor="text-emerald-500"
      size="medium"
      headerAction={headerAction}
      isLoading={loading}
    >
      <div className="space-y-3">
        {todayTasks.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle2 className="h-12 w-12 text-emerald-500/50 mx-auto mb-3" />
            <p className="text-muted-foreground">No tasks for today!</p>
            <p className="text-sm text-muted-foreground/70">You're all caught up</p>
          </div>
        ) : (
          todayTasks.map((task) => {
            const status = getTaskStatus(task);
            return (
              <div
                key={task.id}
                className={cn(
                  "flex items-start gap-3 p-3 rounded-lg border transition-all duration-200 hover:bg-background/50",
                  status === 'overdue'
                    ? "border-red-500/30 bg-red-500/5"
                    : "border-border/50 bg-background/20"
                )}
              >
                <button
                  onClick={() => handleTaskToggle(task)}
                  className="mt-0.5 text-muted-foreground hover:text-emerald-400 transition-colors"
                >
                  <Circle className="h-4 w-4" />
                </button>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-sm truncate">{task.title}</h4>
                    <Badge
                      variant="outline"
                      className={cn("text-xs px-2 py-0", getPriorityColor(task.priority))}
                    >
                      {task.priority}
                    </Badge>
                  </div>

                  {task.description && (
                    <p className="text-xs text-muted-foreground truncate mb-2">
                      {task.description}
                    </p>
                  )}

                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    {status === 'overdue' ? (
                      <div className="flex items-center gap-1 text-red-400">
                        <AlertTriangle className="h-3 w-3" />
                        <span>Overdue</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>
                          {task.dueDate ? format(new Date(task.dueDate), 'h:mm a') : 'No time'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}

        <Button
          variant="ghost"
          onClick={() => navigate("/tasks")}
          className="w-full justify-center gap-2 mt-4 border border-dashed border-emerald-500/30 hover:bg-emerald-500/10 hover:border-emerald-500/50 text-emerald-400"
        >
          <Plus className="h-4 w-4" />
          Add New Task
        </Button>
      </div>
    </DashboardCard>
  );
}
