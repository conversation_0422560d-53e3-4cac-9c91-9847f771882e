import { useNavigate } from "react-router-dom";
import { DashboardCard } from "@/components/shared/DashboardCard";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Timer, 
  Plus, 
  Calendar, 
  BarChart3,
  Zap
} from "lucide-react";

export function QuickActionsCard() {
  const navigate = useNavigate();

  const quickActions = [
    {
      title: "Start Timer",
      description: "Begin a study session",
      icon: Timer,
      action: () => navigate("/productivity"),
      color: "bg-violet-500/20 hover:bg-violet-500/30 border-violet-500/30 text-violet-400"
    },
    {
      title: "Add Task",
      description: "Create a new todo",
      icon: Plus,
      action: () => navigate("/tasks"),
      color: "bg-emerald-500/20 hover:bg-emerald-500/30 border-emerald-500/30 text-emerald-400"
    },
    {
      title: "Schedule Exam",
      description: "Add upcoming test",
      icon: Calendar,
      action: () => navigate("/mock-tests"),
      color: "bg-rose-500/20 hover:bg-rose-500/30 border-rose-500/30 text-rose-400"
    },
    {
      title: "View Analytics",
      description: "Check your progress",
      icon: BarChart3,
      action: () => navigate("/analytics"),
      color: "bg-blue-500/20 hover:bg-blue-500/30 border-blue-500/30 text-blue-400"
    }
  ];

  return (
    <DashboardCard
      title="Quick Actions"
      icon={Zap}
      iconColor="text-yellow-500"
      size="small"
    >
      <div className="space-y-3">
        {quickActions.map((action, index) => {
          const Icon = action.icon;
          return (
            <Button
              key={action.title}
              variant="ghost"
              onClick={action.action}
              className={`w-full justify-start gap-3 h-auto p-3 border transition-all duration-200 ${action.color}`}
            >
              <Icon className="h-4 w-4 flex-shrink-0" />
              <div className="text-left flex-1">
                <p className="font-medium text-sm">{action.title}</p>
                <p className="text-xs opacity-70">{action.description}</p>
              </div>
            </Button>
          );
        })}
      </div>
    </DashboardCard>
  );
}
