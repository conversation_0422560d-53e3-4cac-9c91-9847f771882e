import { useEffect, useState } from "react";
import { DashboardCard } from "@/components/shared/DashboardCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { swotStorage, SWOT_CATEGORIES, SWOT_TYPE_COLORS } from "@/utils/swotStorage";
import { SWOTItem } from "@/types/dashboard";
import {
  Target,
  Plus,
  TrendingUp,
  TrendingDown,
  Lightbulb,
  Alert<PERSON>riangle,
  <PERSON>
} from "lucide-react";
import { cn } from "@/lib/utils";

export function SWOTAnalysisCard() {
  const { user } = useSupabaseAuth();
  const [swotData, setSWotData] = useState({
    strengths: [] as SWOTItem[],
    weaknesses: [] as SWOTItem[],
    opportunities: [] as SWOTItem[],
    threats: [] as SWOTItem[]
  });
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newItem, setNewItem] = useState({
    type: 'strength' as SWOTItem['type'],
    title: '',
    description: '',
    category: ''
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadSWOTData();
    }
  }, [user]);

  const loadSWOTData = () => {
    if (!user) return;

    setLoading(true);
    try {
      const analysis = swotStorage.getSWOTAnalysis(user.id);
      setSWotData({
        strengths: analysis.strengths,
        weaknesses: analysis.weaknesses,
        opportunities: analysis.opportunities,
        threats: analysis.threats
      });
    } catch (error) {
      console.error('Error loading SWOT data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = () => {
    if (!user || !newItem.title.trim()) return;

    try {
      swotStorage.addSWOTItem(user.id, {
        type: newItem.type,
        title: newItem.title.trim(),
        description: newItem.description.trim(),
        category: newItem.category || 'General'
      });

      // Reload data
      loadSWOTData();

      // Reset form
      setNewItem({
        type: 'strength',
        title: '',
        description: '',
        category: ''
      });
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error('Error adding SWOT item:', error);
    }
  };

  const getSWOTIcon = (type: SWOTItem['type']) => {
    switch (type) {
      case 'strength':
        return TrendingUp;
      case 'weakness':
        return TrendingDown;
      case 'opportunity':
        return Lightbulb;
      case 'threat':
        return AlertTriangle;
    }
  };

  const getSWOTCount = (type: SWOTItem['type']) => {
    return swotData[`${type}s` as keyof typeof swotData].length;
  };

  const totalItems = Object.values(swotData).reduce((acc, items) => acc + items.length, 0);

  const headerAction = (
    <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="text-xs hover:bg-orange-500/20 hover:text-orange-400"
        >
          <Plus className="h-3 w-3 mr-1" />
          Add
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add SWOT Item</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="type">Type</Label>
            <Select
              value={newItem.type}
              onValueChange={(value) => setNewItem(prev => ({ ...prev, type: value as SWOTItem['type'] }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="strength">Strength</SelectItem>
                <SelectItem value="weakness">Weakness</SelectItem>
                <SelectItem value="opportunity">Opportunity</SelectItem>
                <SelectItem value="threat">Threat</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={newItem.title}
              onChange={(e) => setNewItem(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Enter a brief title"
            />
          </div>

          <div>
            <Label htmlFor="category">Category</Label>
            <Select
              value={newItem.category}
              onValueChange={(value) => setNewItem(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {SWOT_CATEGORIES.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={newItem.description}
              onChange={(e) => setNewItem(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Provide more details..."
              rows={3}
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button onClick={handleAddItem} className="flex-1">
              Add Item
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsAddDialogOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <DashboardCard
      title="SWOT Analysis"
      icon={Target}
      iconColor="text-orange-500"
      size="medium"
      headerAction={headerAction}
      isLoading={loading}
    >
      <div className="space-y-4">
        {totalItems === 0 ? (
          <div className="text-center py-8">
            <Brain className="h-12 w-12 text-orange-500/50 mx-auto mb-3" />
            <p className="text-muted-foreground">No SWOT analysis yet</p>
            <p className="text-sm text-muted-foreground/70 mb-4">
              Track your strengths, weaknesses, opportunities, and threats
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3">
            {(['strength', 'weakness', 'opportunity', 'threat'] as const).map((type) => {
              const Icon = getSWOTIcon(type);
              const count = getSWOTCount(type);
              const colors = SWOT_TYPE_COLORS[type];

              return (
                <div
                  key={type}
                  className={cn(
                    "p-3 rounded-lg border transition-all duration-200 hover:bg-background/50",
                    colors.bg,
                    colors.border
                  )}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Icon className={cn("h-4 w-4", colors.icon)} />
                    <span className="text-xs font-medium capitalize">{type}s</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className={cn("text-lg font-bold", colors.text)}>
                      {count}
                    </span>
                    {count > 0 && (
                      <Badge
                        variant="outline"
                        className={cn("text-xs px-2 py-0", colors.bg, colors.border, colors.text)}
                      >
                        {count === 1 ? 'item' : 'items'}
                      </Badge>
                    )}
                  </div>

                  {/* Show latest item if exists */}
                  {swotData[`${type}s` as keyof typeof swotData].length > 0 && (
                    <p className="text-xs text-muted-foreground mt-2 truncate">
                      {swotData[`${type}s` as keyof typeof swotData][0].title}
                    </p>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {totalItems > 0 && (
          <div className="pt-2 border-t border-border/50">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total Items</span>
              <span className="font-medium">{totalItems}</span>
            </div>
          </div>
        )}
      </div>
    </DashboardCard>
  );
}
