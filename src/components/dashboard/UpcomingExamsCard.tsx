import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardCard } from "@/components/shared/DashboardCard";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { dDayStorage, DDayExam } from "@/utils/mockTestLocalStorage";
import {
  Calendar,
  Clock,
  Plus,
  AlertTriangle,
  CheckCircle2,
  BookOpen
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format, differenceInDays, differenceInHours, isPast, parseISO } from "date-fns";

export function UpcomingExamsCard() {
  const navigate = useNavigate();
  const { user } = useSupabaseAuth();
  const [upcomingExams, setUpcomingExams] = useState<DDayExam[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadUpcomingExams();
    }
  }, [user]);

  const loadUpcomingExams = () => {
    if (!user) return;

    setLoading(true);
    try {
      const allExams = dDayStorage.getAllExams(user.id);

      // Filter for upcoming exams only
      const upcoming = allExams
        .filter(exam => exam.status === 'upcoming')
        .filter(exam => {
          const examDateTime = parseISO(`${exam.date}T${exam.time}`);
          return !isPast(examDateTime);
        })
        .sort((a, b) => {
          const dateA = parseISO(`${a.date}T${a.time}`);
          const dateB = parseISO(`${b.date}T${b.time}`);
          return dateA.getTime() - dateB.getTime();
        })
        .slice(0, 3); // Show max 3 upcoming exams

      setUpcomingExams(upcoming);
    } catch (error) {
      console.error('Error loading upcoming exams:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTimeUntilExam = (exam: DDayExam) => {
    const examDateTime = parseISO(`${exam.date}T${exam.time}`);
    const now = new Date();

    const daysLeft = differenceInDays(examDateTime, now);
    const hoursLeft = differenceInHours(examDateTime, now);

    if (daysLeft > 0) {
      return {
        value: daysLeft,
        unit: daysLeft === 1 ? 'day' : 'days',
        urgent: daysLeft <= 3
      };
    } else if (hoursLeft > 0) {
      return {
        value: hoursLeft,
        unit: hoursLeft === 1 ? 'hour' : 'hours',
        urgent: true
      };
    } else {
      return {
        value: 0,
        unit: 'Now',
        urgent: true
      };
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'high':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'low':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getPreparationProgress = (exam: DDayExam) => {
    if (!exam.preparationData?.chapters) return 0;

    const totalChapters = exam.preparationData.chapters.length;
    if (totalChapters === 0) return 0;

    const completedChapters = exam.preparationData.chapters.filter(
      chapter => chapter.isCompleted
    ).length;

    return Math.round((completedChapters / totalChapters) * 100);
  };

  const headerAction = (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => navigate("/mock-tests")}
      className="text-xs hover:bg-rose-500/20 hover:text-rose-400"
    >
      View All
    </Button>
  );

  return (
    <DashboardCard
      title="Upcoming Exams"
      icon={Calendar}
      iconColor="text-rose-500"
      size="large"
      headerAction={headerAction}
      isLoading={loading}
    >
      <div className="space-y-4">
        {upcomingExams.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-rose-500/50 mx-auto mb-3" />
            <p className="text-muted-foreground">No upcoming exams</p>
            <p className="text-sm text-muted-foreground/70 mb-4">Schedule your next exam to start preparing</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/mock-tests")}
              className="border-rose-500/30 hover:bg-rose-500/10 text-rose-400"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Exam
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {upcomingExams.map((exam) => {
              const timeLeft = getTimeUntilExam(exam);
              const progress = getPreparationProgress(exam);

              return (
                <div
                  key={exam.id}
                  className={cn(
                    "p-4 rounded-lg border transition-all duration-200 hover:bg-background/50",
                    timeLeft.urgent
                      ? "border-red-500/30 bg-red-500/5"
                      : "border-border/50 bg-background/20"
                  )}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-sm truncate mb-1">{exam.name}</h4>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{format(parseISO(`${exam.date}T${exam.time}`), 'MMM dd, h:mm a')}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge
                        variant="outline"
                        className={cn("text-xs px-2 py-0", getPriorityColor(exam.priority))}
                      >
                        {exam.priority}
                      </Badge>

                      <div className={cn(
                        "text-right",
                        timeLeft.urgent ? "text-red-400" : "text-muted-foreground"
                      )}>
                        <p className="text-lg font-bold leading-none">
                          {timeLeft.value}
                        </p>
                        <p className="text-xs">{timeLeft.unit}</p>
                      </div>
                    </div>
                  </div>

                  {/* Preparation Progress */}
                  {exam.preparationData?.chapters && exam.preparationData.chapters.length > 0 && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <BookOpen className="h-3 w-3 text-blue-400" />
                          <span className="text-xs text-muted-foreground">Preparation</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{progress}%</span>
                      </div>
                      <Progress value={progress} className="h-1.5" />
                    </div>
                  )}

                  {/* Categories */}
                  {exam.categories && exam.categories.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {exam.categories.slice(0, 3).map((category, index) => (
                        <span
                          key={index}
                          className="text-xs px-2 py-1 bg-violet-500/20 text-violet-400 rounded-full"
                        >
                          {category}
                        </span>
                      ))}
                      {exam.categories.length > 3 && (
                        <span className="text-xs px-2 py-1 bg-muted/50 text-muted-foreground rounded-full">
                          +{exam.categories.length - 3} more
                        </span>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {upcomingExams.length > 0 && (
          <Button
            variant="ghost"
            onClick={() => navigate("/mock-tests")}
            className="w-full justify-center gap-2 mt-4 border border-dashed border-rose-500/30 hover:bg-rose-500/10 hover:border-rose-500/50 text-rose-400"
          >
            <Plus className="h-4 w-4" />
            Add New Exam
          </Button>
        )}
      </div>
    </DashboardCard>
  );
}
